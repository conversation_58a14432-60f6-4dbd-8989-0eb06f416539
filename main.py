import duckdb

# Create a connection
con = duckdb.connect(database=':memory:')

# Create table and load data from the actual CSV file
sql = """
CREATE TABLE invoices AS 
SELECT * FROM read_csv_auto('/Users/<USER>/Downloads/ec1d7d78-c429-4323-a227-e04ebba66d2b_9715660535834148.csv');
"""
con.execute(sql)

# View the actual schema
print("Actual Table Schema:")
schema_df = con.execute("DESCRIBE invoices").fetchdf()
print(schema_df)

# View record count before deduplication
print("\nTotal Records Before Deduplication:")
count_result = con.execute("SELECT COUNT(*) FROM invoices").fetchall()
print(count_result[0][0])

# Remove duplicates based on InvoiceNumber and ChargeName combination
# Keep only the first occurrence of each combination
deduplicated_query = """
WITH deduplicated_data AS (
    SELECT *,
           ROW_NUMBER() OVER (
               PARTITION BY InvoiceNumber, ChargeName 
               ORDER BY InvoiceId
           ) as row_num
    FROM invoices
)
SELECT 
    InvoiceNumber,
    COUNT(*) as record_count,
    COUNT(DISTINCT ChargeName) as unique_charges,
    STRING_AGG(DISTINCT ChargeName, ', ') as charge_names
FROM deduplicated_data 
WHERE row_num = 1
GROUP BY InvoiceNumber
ORDER BY InvoiceNumber;
"""

print("\nDeduplicated Data Grouped by Invoice Number:")
result_df = con.execute(deduplicated_query).fetchdf()
print(result_df)

# Show count after deduplication
count_after_query = """
WITH deduplicated_data AS (
    SELECT *,
           ROW_NUMBER() OVER (
               PARTITION BY InvoiceNumber, ChargeName 
               ORDER BY InvoiceId
           ) as row_num
    FROM invoices
)
SELECT COUNT(*) FROM deduplicated_data WHERE row_num = 1;
"""

print("\nTotal Records After Deduplication:")
count_after = con.execute(count_after_query).fetchall()
print(count_after[0][0])

# If you want to see the actual deduplicated records (not just grouped summary):
deduplicated_records_query = """
WITH deduplicated_data AS (
    SELECT *,
           ROW_NUMBER() OVER (
               PARTITION BY InvoiceNumber, ChargeName 
               ORDER BY InvoiceId
           ) as row_num
    FROM invoices
)
SELECT * FROM deduplicated_data 
WHERE row_num = 1
ORDER BY InvoiceNumber, ChargeName;
"""

print("\nFirst 10 Deduplicated Records:")
deduplicated_df = con.execute(deduplicated_records_query).fetchdf()
print(deduplicated_df.head(10))

# Optional: Show some statistics about duplicates
duplicate_stats_query = """
SELECT 
    InvoiceNumber,
    ChargeName,
    COUNT(*) as duplicate_count
FROM invoices
GROUP BY InvoiceNumber, ChargeName
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, InvoiceNumber;
"""

print("\nDuplicate Statistics (Invoice-Charge combinations with more than 1 record):")
duplicate_stats_df = con.execute(duplicate_stats_query).fetchdf()
print(duplicate_stats_df.head(10))

# Export duplicate_stats_df to CSV
duplicate_stats_df.to_csv('duplicate_stats.csv', index=False)
print(f"\nDuplicate statistics exported to 'duplicate_stats.csv'")
print(f"Total duplicate combinations found: {len(duplicate_stats_df)}")

con.close()