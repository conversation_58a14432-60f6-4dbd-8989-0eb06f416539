#%%
import duckdb
con = duckdb.connect(database=':memory:')
result = con.execute("SELECT * FROM read_csv_auto('data/BAU-1199_v3_with_duplicates.csv')").fetchdf()
print(result.count())
con.close()
#%%
import pandas as pd

# Data
data = {
'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
'Age': [22, 78, 22, 30, 45, 30, 35, 40],
'Gender': ['Male', 'Female', 'Male', 'Female', 'Female', 'Female', 'Male', 'Female'],
'City': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego'],
'Occupation': ['Engineer', 'Doctor', 'Teacher', 'Nurse', 'Architect', 'Lawyer', 'Accountant', 'Scientist']
}

# Create a DataFrame
df = pd.DataFrame(data)

# Display the DataFrame
df